# AI Program E2E Evaluation Tool

一个用于执行AI程序端到端评估的工具，支持批量执行指令并生成详细的执行报告。支持 `text_infer_v3` 和 `text_infer_xml` 两种执行模式。

## 功能特性

- **多模式支持**：支持 `text_infer_v3` 和 `text_infer_xml` 两种执行模式
- **详细报告**：生成包含执行时间、状态码、响应内容的详细报告
- **日志记录**：实时显示执行进度和详细日志

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 基本用法

```bash
# 使用默认模式 (text_infer_v3)
python execute_instructions.py merged_result.json

# 使用 text_infer_xml 模式
python execute_instructions.py merged_result.json --mode text_infer_xml

# 查看帮助信息
python execute_instructions.py --help
```

### 命令行参数

- `json_file`: 包含指令的JSON文件路径（必需）
- `--mode`: 执行模式，可选值：
  - `text_infer_v3`（默认）：使用生产环境配置
  - `text_infer_xml`：使用XML推理模式配置

## 输入文件格式

工具支持多种JSON格式的输入文件：

### 格式1：对象数组（推荐）
```json
[
  {
    "id": 2238,
    "instruction": "点一份吉士炒双蛋堡套餐，在咸阳机场T3航站楼夹层餐厅取餐",
    "rtx": "v_lijvnwang",
    "appId": "wx25f982a55e60a540"
  },
  {
    "id": 2239,
    "instruction": "查看今天的天气预报"
  }
]
```

### 格式2：包含instructions字段的对象
```json
{
  "instructions": [
    {
      "text": "点一份吉士炒双蛋堡套餐",
      "id": 1
    },
    {
      "text": "查看今天的天气预报",
      "id": 2
    }
  ]
}
```

### 格式3：简单字符串数组
```json
[
  "点一份吉士炒双蛋堡套餐，在咸阳机场T3航站楼夹层餐厅取餐",
  "查看今天的天气预报"
]
```

## 输出结果

执行完成后，工具会生成一个时间戳命名的结果文件（如 `results_1703123456.json`），包含以下信息：

```json
{
  "results": [
    {
      "instruction": "点一份吉士炒双蛋堡套餐，在咸阳机场T3航站楼夹层餐厅取餐",
      "id": 1,
      "success": true,
      "execution_time": 2.345,
      "id": 2238,
      "status_code": 200,
      "response": "执行成功的响应内容..."
    },
    {
      "instruction": "查看今天的天气预报",
      "id": 2,
      "success": false,
      "execution_time": 1.234,
      "error": "连接超时错误信息",
      "status_code": 500
    }
  ]
}
```

## 执行模式说明

### text_infer_v3 模式（默认）
- **服务器**: `http://mmfinderdrsandboxfdsvr.production.polaris/v1/agent/run_headless`
- **用途**: 生产环境的文本推理
- **配置**: 使用标准的生产环境参数

### text_infer_xml 模式
- **服务器**: `http://21.90.62.245/v1/agent/run_headless`
- **用途**: XML格式的文本推理
- **配置**: 使用特定的模型和代理配置
- **模型**: `eval_jhinzhou-Qwen3-32B-0609-10`

## 配置说明

### 日志级别
默认使用 INFO 级别，显示：
- 执行进度
- 请求状态
- 响应摘要（前200字符）
- 错误信息

## 错误处理

工具包含完善的错误处理机制：

- **连接错误**: 无法连接到服务器
- **超时错误**: 请求超过5分钟未响应
- **HTTP错误**: 服务器返回错误状态码
- **JSON解析错误**: 输入文件格式错误
- **文件不存在**: 指定的JSON文件不存在

## 示例

```bash
# 执行示例文件
python execute_instructions.py merged_result.json

# 输出示例
2024-01-01 10:00:00,123 - INFO - Using mode: text_infer_v3
2024-01-01 10:00:00,124 - INFO - Step 1/2
2024-01-01 10:00:00,125 - INFO - Executing instruction: 点一份吉士炒双蛋堡套餐...
2024-01-01 10:00:02,456 - INFO - Request executed successfully
2024-01-01 10:00:02,457 - INFO - Response: {"status": "success", "data": ...
2024-01-01 10:00:04,789 - INFO - All instructions executed. Results saved to results_1703123456.json
```
