#!/usr/bin/env python3
import json
import sys
import time
import logging
import requests
import argparse

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def execute_http_request(app_id, instruction, mode="text_infer_v3"):
    """Execute an HTTP request with the given instruction."""
    start_time = time.time()
    headers = {"Content-Type": "application/json"}

    # Configure different settings based on mode
    if mode == "text_infer_v3":
        url = "http://mmfinderdrsandboxagentsvr.production.polaris:80/v1/agent/run_headless"
        data = {
            "app_id": app_id,
            "run_mode": "text_infer_v3",
            "from_username": "wxid_adldhsbu7ip312",
            "request_id": f"test_session_id_{int(time.time())}",
            "headless_mode": "2",
            "uin": "3194644195",
            "username": "wxid_adldhsbu7ip312",
            "instruction": instruction,
            "model_name": "llm_luban_minip_base4k_v20250424_01-0506-21",
            "base_url": "http://drhttpsvr.polaris:8000/v1/llm_luban_minip_base4k_v20250424_01-0506-21/",
            "use_vlt": "1",
            "vlt_base_url": "http://drhttpsvr.polaris:8000/v1/llm_luban_xiaochengxu_qwen25vl32b_v20250605_02_checkpoint-650_export-0606-15/",
            "vlt_model_name": "llm_luban_xiaochengxu_qwen25vl32b_v20250605_02_checkpoint-650_export-0606-15",
            "use_wait_model": "1",
            "wait_model_base_url": "http://drhttpsvr.polaris:8000/v1/llm-luban-wait3B_606_xd_ck700_export-0611-16",
            "wait_model_name": "llm-luban-wait3B_606_xd_ck700_export-0611-16",
            "vlt_base_url_v1":"http://drhttpsvr.polaris:8000/v1/llm-luban_xiaochengxu_qwen25vl32b_v20250613_400_v3_export-0613-15/",
            "vlt_model_name_v1":"llm-luban_xiaochengxu_qwen25vl32b_v20250613_400_v3_export-0613-15",
            "prompt_vlt": """你是一个小程序操作助手，上述图像是按顺序操作任务指令，最近一张是当前图像。当前指令是{}，请问在当前图像里我要怎么操作？
    当前只能有一种输出选项：只能回复接下来的一个动作，不能回复多个动作，并给出在页面的精确像素坐标（x y），最终输出格式严格遵循<think>xxx</think><answer>xxx</answer>这个格式。answer动作只能在以下6 类集合选>择：1. click x y(代表点击像素坐标 x,y)，2. input x y t（代表在x y 选中输入框并输入文本t） 3. finish（代表任务成功结束） 4. stop（代表任务无法进行 进程终止）5. scroll x（代表下滑x个像素，x为负数代表上滑|x|个
像素）6. wait t（代表当前页面处于加载中，可以等待t秒）。
    思考内容：首先通过对话上下文判断上一步的操作是否生效，如果上一步操作无效，需要反思并采取正确的动作，例如 1.  input x y t 后页面是否有显示 input 的内容t，没有的话则操作未生效，考虑网页不支持本步input 而>应该采取click x y 操作使用点击小程序拉起的键盘输入 2. click x y 后操作未生效考虑点击位置无效，应该采取点击其他位置click x1 y1实现指令 3. assistant重复输入相同命令表示当前操作无效，需要采取其他方法实现指令 4. 需要详细检查 input 的内容 t 是否正确显示在页面中 5. 最近两张图像没有变化时需要反思动作是否正确。
    注意：
       1.如果当前页面支持搜索功能 ，优先使用搜索来实现任务目标。
       2.如果当前页面是支付二维码页面 ，则可以考虑任务完成，结束流程。
       3.如果当前页面处于确认付款阶段 ，请检查购物车中的商品是否符合任务要求：若不符合要求，请先删除不需要的商品,再继续下一步操作。"""
        }
    elif mode == "text_infer_xml":
        url = "http://************/v1/agent/run_headless"
        data = {
            "app_id": app_id,
            "model_name": "llm-luban-Qwen3-32B-0616-14",
            "base_url": "http://drhttpsvr.polaris:8000/v1/llm-luban-Qwen3-32B-0616-14/",
            "run_mode": "text_infer_xml",
            "from_username": "wxid_adldhsbu7ip312",
            "request_id": "test_session_id_{int(time.time())}",
            "prompt": """你是一个小程序操作助手，现在需要操作小程序来完成用户的需求，整个过程可能需要进行多步的操作和尝试才能完成
请尽可能的进行尝试和探索来完成用户的需求

## 输入信息
- 用户输入：任务指令或者补充信息，将会包含在<user_input></user_input>标签中
- 小程序状态：根据小程序页面的dom树进行解析和简化后的结构化文本，将会包含在<state_i></state_i>标签中（代表当前操作第i步）
  - 每个可操作的元素开头都会有一个唯一的id标识在[]内（比如`[n]`代表当前元素的id是n）
  - 会用缩进代表页面上的一些层级关系
  - clickable代表元素可进行点击；typeable代表元素可进行输入；scrollable代表元素可进行滑动；有时这些事件也会继承给层级内部的子元素

## 任务
1. 你需要以完成用户的需求为主要目标，对当前小程序的状态进行分析，并从tools中选择合适的tool对小程序进行操作
2. 优先使用搜索能力来查找需要的内容，有的搜索框不会标注typeable，只会有clickable，你需要根据文字描述进行合理的推断
3. 每次操作完成后，你将会收到最新的小程序状态信息
4. 整个操作过程需要反复迭代和尝试，直到任务完成

## 注意事项
- 购物车内有其他无关商品时，尝试进行剔除
- 购物、点单、消费任务进入到支付页面即可terminate，无需点击支付操作
- 你只对最新的state中展示的元素编号进行操作
- 如果当页面元素中出现有关：*服务使用条款*、*隐私协议*等信息时，**点击上层/前面的同意按钮**（注意不是条款内容），否则其他的操作（登录、支付等）可能无法进行点击
- 如果发现无法完成任务，转变思路再尝试一下
- 由于一些不可抗力，有些元素会丢失掉text信息
- 页面状态为空时，wait一段时间后再操作
- 不要擅自篡改或简化用户的需求
- 需要切换位置时，如果没有明确的可操作元素，可尝试点击地点来切换位置
- 如果可以进行搜索，优先考虑使用搜索功能

**请将当前任务状态的总结以及下一步的规划输出在<think></think>标签内，字数小于150字**""",
            "vlt_base_url":"http://**************:8000/ocr_caption/",
            "headless_mode": "2",
            "uin": "3194644195",
            "username": "wxid_adldhsbu7ip312",
            "instruction": instruction
        }
    else:
        raise ValueError(f"Unsupported mode: {mode}. Supported modes: text_infer_v3, text_infer_xml")

    logger.info(f"Executing instruction: {instruction}")

    try:
        # Execute the HTTP request using requests
        response = requests.post(
            url,
            json=data,
            headers=headers,
            timeout=1800  # 30 minute timeout
        )
        execution_time = time.time() - start_time

        # Check if request was successful
        response.raise_for_status()

        response_text = response.text
        logger.info("Request executed successfully")
        logger.info(f"Response: {response_text[:200]}...")  # Show first 200 chars

        return {
            "success": True,
            "response": response_text,
            "execution_time": execution_time,
            "status_code": response.status_code
        }

    except requests.exceptions.Timeout:
        execution_time = time.time() - start_time
        error_msg = "Request timed out after 30 minutes"
        logger.error(error_msg)
        return {
            "success": False,
            "error": error_msg,
            "execution_time": execution_time
        }
    except requests.exceptions.ConnectionError:
        execution_time = time.time() - start_time
        error_msg = "Connection error - unable to reach the server"
        logger.error(error_msg)
        return {
            "success": False,
            "error": error_msg,
            "execution_time": execution_time
        }
    except requests.exceptions.HTTPError as e:
        execution_time = time.time() - start_time
        error_msg = f"HTTP error {e.response.status_code}: {e.response.text}"
        logger.error(error_msg)
        return {
            "success": False,
            "error": error_msg,
            "execution_time": execution_time,
            "status_code": e.response.status_code
        }
    except Exception as e:
        execution_time = time.time() - start_time
        error_msg = f"Unexpected error: {str(e)}"
        logger.error(error_msg)
        return {
            "success": False,
            "error": error_msg,
            "execution_time": execution_time
        }

def main():
    parser = argparse.ArgumentParser(description='Execute instructions from JSON file')
    parser.add_argument('json_file', help='JSON file containing instructions')
    parser.add_argument('--mode', choices=['text_infer_v3', 'text_infer_xml'],
                       default='text_infer_v3',
                       help='Execution mode (default: text_infer_v3)')

    args = parser.parse_args()
    json_file = args.json_file
    mode = args.mode

    logger.info(f"Using mode: {mode}")

    try:
        # Load instructions from JSON file
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)

        # Extract instructions from the JSON
        instructions = []
        if isinstance(data, dict) and "instructions" in data:
            instructions = data["instructions"]
        elif isinstance(data, list):
            instructions = data
        else:
            # Try to find instructions in the JSON structure
            for key, value in data.items():
                if key == "instruction" or key == "instructions":
                    if isinstance(value, list):
                        instructions = value
                    else:
                        instructions = [value]
                    break

        if not instructions:
            logger.error("No instructions found in the JSON file")
            sys.exit(1)

        # Prepare results list
        results = []

        # Execute each instruction
        for i, instruction in enumerate(instructions):
            logger.info(f"Step {i+1}/{len(instructions)}")
            if isinstance(instruction, dict) and "instruction" in instruction:
                app_id = instruction["appId"]
                instruction_text = instruction["instruction"]
            elif isinstance(instruction, dict) and "text" in instruction:
                instruction_text = instruction["text"]
            elif isinstance(instruction, str):
                instruction_text = instruction
            else:
                logger.warning(f"Skipping invalid instruction format: {instruction}")
                continue

            response = execute_http_request(app_id, instruction_text, mode)

            # Record result
            result_entry = {
                "instruction": instruction_text,
                "step": i+1,
                "success": response["success"],
                "execution_time": response["execution_time"]
            }

            # Add ID if available
            if isinstance(instruction, dict) and "id" in instruction:
                result_entry["id"] = instruction["id"]

            # Add status code if available
            if "status_code" in response:
                result_entry["status_code"] = response["status_code"]

            if response["success"]:
                result_entry["response"] = response["response"]
            else:
                result_entry["error"] = response["error"]

            results.append(result_entry)

            # Optional: add delay between requests
            if i < len(instructions) - 1:
                time.sleep(2)  # 2 second delay between requests

        # Write results to JSON file
        output_file = f"results_{int(time.time())}.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump({"results": results}, f, ensure_ascii=False, indent=2)

        logger.info(f"All instructions executed. Results saved to {output_file}")

    except FileNotFoundError:
        logger.error(f"File not found: {json_file}")
        sys.exit(1)
    except json.JSONDecodeError:
        logger.error(f"Invalid JSON format in file: {json_file}")
        sys.exit(1)
    except Exception as e:
        logger.error(f"An error occurred: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
